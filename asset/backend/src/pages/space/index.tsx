import UserModal from '@/components/user-modal';
import { Content, ModalForm, Table, Space, RequestButton, User } from '@topthink/common';
import dayjs from 'dayjs';
import { Badge, Form } from 'react-bootstrap';

export default function () {
    return <Content title={'空间'}>
        <Table
            search={{
                fields: ['owner', 'plan', 'expire_time'],
                ui: {
                    plan: {
                        'ui:widget': 'select',
                        'ui:options': {
                            enumOptions: [
                                {
                                    value: 'personal',
                                    label: '个人版'
                                },
                                {
                                    value: 'trial',
                                    label: '企业版试用'
                                },
                                {
                                    value: 'team',
                                    label: '团队版'
                                },
                                {
                                    value: 'enterprise',
                                    label: '企业版'
                                }
                            ],
                        }
                    },
                    expire_time: {
                        'ui:widget': 'select',
                        'ui:options': {
                            enumOptions: [
                                {
                                    value: 'expired',
                                    label: '已过期'
                                },
                                {
                                    value: 'expiring',
                                    label: '即将过期'
                                },
                                {
                                    value: 'valid',
                                    label: '未过期'
                                },
                            ],
                        }
                    }
                }
            }}
            source={'/space'}
            columns={[
                {
                    title: 'ID',
                    dataIndex: 'id',
                    width: 60
                },
                {
                    title: '名称',
                    dataIndex: 'owner',
                    render({ value, record }) {
                        return <Space>
                            <a href={record.url} target={'_blank'}>{value.name}</a>
                            {record.block_time && <Badge bg={'danger'}>已禁用</Badge>}
                        </Space>;
                    }
                },
                {
                    title: '创始人',
                    dataIndex: 'owners',
                    align: 'center',
                    width: 100,
                    render({ value }) {
                        return <Space>
                            {value.map((user: User) => {
                                return <UserModal key={user.id} user={user} text={<img className='rounded-circle' width={24} height={24} src={user.avatar} />} />;
                            })}
                        </Space>;
                    }
                },
                {
                    title: '版本',
                    dataIndex: 'plan',
                    align: 'center',
                    width: 100,
                    render({ record }) {
                        if (record.owner_type === 'app\\model\\User') {
                            return '个人版';
                        } else {
                            const title = {
                                trial: '企业版试用',
                                team: '团队版',
                                enterprise: '企业版',
                            };
                            return title[record.plan as keyof typeof title];
                        }
                    },
                },
                {
                    title: '扩容',
                    dataIndex: 'size',
                    align: 'center',
                    width: 100,
                    render({ value, record }) {
                        if (record.owner_type === 'app\\model\\User') {
                            return '--';
                        } else {
                            return `${value} GiB | ${record.tokens} K`;
                        }
                    }
                },
                {
                    title: '有效期',
                    dataIndex: 'expire_time',
                    align: 'center',
                    width: 100,
                    render({ record }) {
                        if (record.owner_type === 'app\\model\\User') {
                            return '--';
                        } else {
                            return <span title={record.expire_time}>{dayjs(record.expire_time).format('YYYY-MM-DD')}</span>;
                        }
                    },
                },
                {
                    title: '创建时间',
                    dataIndex: 'create_time',
                    align: 'center',
                    width: 100,
                    render({ record }) {
                        return <span title={record.create_time}>{dayjs(record.create_time).format('YYYY-MM-DD')}</span>;
                    },
                },
                {
                    title: '内容审核',
                    dataIndex: 'moderation',
                    align: 'center',
                    width: 80,
                    render({ value, action, record }) {
                        if (value === -1) {
                            return '--';
                        }
                        return value ? <RequestButton
                            variant={'link'}
                            url={`/space/${record.id}/moderation/off`}
                            onSuccess={action.reload}
                            method={'post'}
                        >
                            <Form.Check key={value} type='switch' defaultChecked />
                        </RequestButton> : <RequestButton
                            variant={'link'}
                            url={`/space/${record.id}/moderation/on`}
                            onSuccess={action.reload}
                            method={'post'}
                        >
                            <Form.Check key={value} type='switch' />
                        </RequestButton>;
                    }
                },
                {
                    title: '新功能体验',
                    dataIndex: 'preemptive',
                    align: 'center',
                    width: 110,
                    render({ value, action, record }) {
                        return value ? <RequestButton
                            variant={'link'}
                            url={`/space/${record.id}/preemptive/off`}
                            onSuccess={action.reload}
                            method={'post'}
                        >
                            <Form.Check role='button' key={value} type='switch' defaultChecked />
                        </RequestButton> : <RequestButton
                            variant={'link'}
                            url={`/space/${record.id}/preemptive/on`}
                            onSuccess={action.reload}
                            method={'post'}
                        >
                            <Form.Check role='button' key={value} type='switch' />
                        </RequestButton>;
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    align: 'right',
                    width: 120,
                    render({ record, action }) {
                        return <Space>
                            {record.owner_type === 'app\\model\\Organization' && <ModalForm
                                action={`/space/${record.id}/features`}
                                text={'功能设置'}
                                onSuccess={action.reload}
                                formData={{
                                    model: record.features?.model || ''
                                }}
                                schema={{
                                    type: 'object',
                                    properties: {
                                        model: {
                                            type: 'string',
                                            title: 'AI模型',
                                            description: '设置空间使用的AI模型名称'
                                        }
                                    }
                                }}
                            />}
                            {record.owner_type === 'app\\model\\Organization' && <ModalForm
                                action={`/space/${record.id}/plan`}
                                text={'版本设置'}
                                onSuccess={action.reload}
                                formData={{
                                    plan: record.plan,
                                    expire_time: record.expire_time,
                                    size: record.size,
                                    tokens: record.tokens
                                }}
                                schema={{
                                    type: 'object',
                                    required: ['plan'],
                                    properties: {
                                        plan: {
                                            type: 'string',
                                            title: '版本',
                                            enum: [
                                                'trial',
                                                'team',
                                                'enterprise'
                                            ],
                                        },
                                        expire_time: {
                                            type: 'string',
                                            format: 'date-time',
                                            title: '有效期'
                                        },
                                        size: {
                                            type: 'number',
                                            title: '扩容大小'
                                        },
                                        tokens: {
                                            type: 'number',
                                            title: 'Tokens额度提升'
                                        },
                                    }
                                }}
                                uiSchema={{
                                    plan: {
                                        'ui:options': {
                                            enumNames: ['企业版试用', '团队版', '企业版']
                                        }
                                    },
                                    size: {
                                        'ui:options': {
                                            suffix: 'GiB'
                                        }
                                    },
                                    tokens: {
                                        'ui:options': {
                                            suffix: 'K'
                                        }
                                    }
                                }}
                            />}
                            {record.block_time ? <RequestButton
                                url={`/space/${record.id}/unblock`}
                                onSuccess={action.reload}
                                confirm={'确定要恢复吗？'}
                                method={'post'}>恢复</RequestButton> :
                                <RequestButton
                                    url={`/space/${record.id}/block`}
                                    onSuccess={action.reload}
                                    confirm={'确定要禁用吗？'}
                                    method={'post'}>禁用</RequestButton>}
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
}
