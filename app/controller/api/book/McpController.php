<?php

namespace app\controller\api\book;

use think\Response;

class <PERSON><PERSON>p<PERSON><PERSON>roll<PERSON> extends Controller
{
    /**
     * MCP协议统一入口点
     */
    public function index()
    {
        // 处理OPTIONS请求
        if ($this->request->isOptions()) {
            return Response::create()->header([
                'Access-Control-Allow-Origin'  => '*',
                'Access-Control-Allow-Methods' => 'GET, POST, OPTIONS',
                'Access-Control-Allow-Headers' => 'Content-Type, Authorization',
            ]);
        }

        // 处理GET请求 - 返回服务器信息
        if ($this->request->isGet()) {
            return $this->getServerInfo();
        }

        // 处理POST请求 - JSON-RPC协议
        if ($this->request->isPost()) {
            return $this->handleJsonRpc();
        }

        return $this->errorResponse(-32600, 'Invalid Request');
    }

    /**
     * 获取服务器信息
     */
    private function getServerInfo()
    {
        $this->authorized('browse', $this->book);

        return json([
            'name'         => 'TopThink Wiki Knowledge Base',
            'version'      => '1.0.0',
            'description'  => '知识库文档搜索服务',
            'capabilities' => [
                'tools' => [
                    'listChanged' => false,
                ],
            ],
        ]);
    }

    /**
     * 处理JSON-RPC请求
     */
    private function handleJsonRpc()
    {
        $this->authorized('browse', $this->book);

        $input = $this->request->getContent();
        $data  = json_decode($input, true);

        if (!$data || !isset($data['jsonrpc']) || $data['jsonrpc'] !== '2.0') {
            return $this->jsonRpcError(null, -32600, 'Invalid Request');
        }

        $id     = $data['id'] ?? null;
        $method = $data['method'] ?? '';
        $params = $data['params'] ?? [];

        try {
            switch ($method) {
                case 'tools/list':
                    return $this->jsonRpcSuccess($id, $this->getToolsList($params));
                case 'tools/call':
                    return $this->jsonRpcSuccess($id, $this->callTool($params));
                default:
                    return $this->jsonRpcError($id, -32601, 'Method not found');
            }
        } catch (\Exception $e) {
            return $this->jsonRpcError($id, -32603, 'Internal error: ' . $e->getMessage());
        }
    }

    /**
     * 获取工具列表
     */
    private function getToolsList($params)
    {
        $tools = [
            [
                'name'        => 'search_documents',
                'title'       => "文档搜索",
                'description' => "在《{$this->book->name}》知识库中搜索相关文档内容，支持关键词搜索和语义搜索。该知识库包含丰富的技术文档和资料，可以帮助您快速找到所需信息。",
                'inputSchema' => [
                    'type'       => 'object',
                    'properties' => [
                        'query'           => [
                            'type'        => 'string',
                            'description' => '搜索关键词或问题，支持自然语言查询',
                        ],
                        'limit'           => [
                            'type'        => 'integer',
                            'description' => '返回结果数量限制，默认为3',
                            'default'     => 3,
                            'minimum'     => 1,
                            'maximum'     => 10,
                        ],
                        'score_threshold' => [
                            'type'        => 'number',
                            'description' => '相似度阈值，默认为0.8',
                            'default'     => 0.8,
                            'minimum'     => 0.0,
                            'maximum'     => 1.0,
                        ],
                    ],
                    'required'   => ['query'],
                ],
            ],
        ];

        return [
            'tools' => $tools,
        ];
    }

    /**
     * 调用工具
     */
    private function callTool($params)
    {
        if (!isset($params['name'])) {
            throw new \Exception('Missing required parameter: name');
        }

        $toolName  = $params['name'];
        $arguments = $params['arguments'] ?? [];

        switch ($toolName) {
            case 'search_documents':
                return $this->handleSearchDocuments($arguments);
            default:
                throw new \Exception("Unknown tool: {$toolName}");
        }
    }

    /**
     * 处理文档搜索
     */
    private function handleSearchDocuments($arguments)
    {
        // 验证参数
        if (empty($arguments['query'])) {
            throw new \Exception('Missing required parameter: query');
        }

        $query          = $arguments['query'];
        $limit          = $arguments['limit'] ?? 3;
        $scoreThreshold = $arguments['score_threshold'] ?? 0.8;

        // 限制参数范围
        $limit          = max(1, min(10, $limit));
        $scoreThreshold = max(0.0, min(1.0, $scoreThreshold));

        // 使用向量搜索
        $results = $this->book->getPages()->searchByVector($query, null, $limit, $scoreThreshold);

        return [
            'content' => [
                [
                    'type' => 'text',
                    'text' => json_encode($results),
                ],
            ],
        ];
    }

    /**
     * JSON-RPC成功响应
     */
    private function jsonRpcSuccess($id, $result)
    {
        return json([
            'jsonrpc' => '2.0',
            'id'      => $id,
            'result'  => $result,
        ]);
    }

    /**
     * JSON-RPC错误响应
     */
    private function jsonRpcError($id, $code, $message)
    {
        return json([
            'jsonrpc' => '2.0',
            'id'      => $id,
            'error'   => [
                'code'    => $code,
                'message' => $message,
            ],
        ]);
    }

    /**
     * 返回协议错误
     */
    private function errorResponse($code, $message)
    {
        return Response::create([
            'error' => [
                'code'    => $code,
                'message' => $message,
            ],
        ], 'json')->code(400);
    }
}
