<?php

namespace app\controller\api\book;

use think\Response;

class <PERSON><PERSON>p<PERSON><PERSON>roll<PERSON> extends Controller
{
    /**
     * MCP协议统一入口点
     */
    public function index()
    {
        // 处理OPTIONS请求
        if ($this->request->isOptions()) {
            return Response::create()->header([
                'Access-Control-Allow-Origin' => '*',
                'Access-Control-Allow-Methods' => 'GET, POST, OPTIONS',
                'Access-Control-Allow-Headers' => 'Content-Type, Authorization',
            ]);
        }

        // 处理GET请求 - 返回服务器信息
        if ($this->request->isGet()) {
            return $this->getServerInfo();
        }

        // 处理POST请求 - JSON-RPC协议
        if ($this->request->isPost()) {
            return $this->handleJsonRpc();
        }

        return $this->errorResponse(-32600, 'Invalid Request');
    }

    /**
     * 获取服务器信息
     */
    private function getServerInfo()
    {
        $this->authorized('browse', $this->book);

        // 获取文档的基本信息
        $bookInfo = [
            'name' => $this->book->name,
            'description' => $this->book->description ?: '暂无描述',
            'visibility' => $this->book->visibility_level == 1 ? '公开' : '私有',
            'updated_at' => $this->book->update_time
        ];

        return json([
            'name' => "TopThink Wiki - {$this->book->name}",
            'version' => '1.0.0',
            'description' => "《{$this->book->name}》知识库文档搜索服务。{$bookInfo['description']} 该知识库为{$bookInfo['visibility']}文档，最后更新时间：{$bookInfo['updated_at']}。",
            'book' => $bookInfo,
            'capabilities' => [
                'tools' => [
                    'listChanged' => false
                ]
            ]
        ]);
    }

    /**
     * 处理JSON-RPC请求
     */
    private function handleJsonRpc()
    {
        $this->authorized('browse', $this->book);

        $input = $this->request->getContent();
        $data = json_decode($input, true);

        if (!$data || !isset($data['jsonrpc']) || $data['jsonrpc'] !== '2.0') {
            return $this->jsonRpcError(null, -32600, 'Invalid Request');
        }

        $id = $data['id'] ?? null;
        $method = $data['method'] ?? '';
        $params = $data['params'] ?? [];

        try {
            switch ($method) {
                case 'tools/list':
                    return $this->jsonRpcSuccess($id, $this->getToolsList($params));
                case 'tools/call':
                    return $this->jsonRpcSuccess($id, $this->callTool($params));
                default:
                    return $this->jsonRpcError($id, -32601, 'Method not found');
            }
        } catch (\Exception $e) {
            return $this->jsonRpcError($id, -32603, 'Internal error: ' . $e->getMessage());
        }
    }

    /**
     * 获取工具列表
     */
    private function getToolsList($params)
    {
        $tools = [
            [
                'name' => 'search_documents',
                'title' => "文档搜索 - {$this->book->name}",
                'description' => "在《{$this->book->name}》知识库中搜索相关文档内容，支持关键词搜索和语义搜索。该知识库包含丰富的技术文档和资料，可以帮助您快速找到所需信息。",
                'inputSchema' => [
                    'type' => 'object',
                    'properties' => [
                        'query' => [
                            'type' => 'string',
                            'description' => "在《{$this->book->name}》中搜索的关键词、问题或主题。支持自然语言查询，例如：'如何使用API'、'安装步骤'、'配置方法'等"
                        ],
                        'limit' => [
                            'type' => 'integer',
                            'description' => '返回搜索结果的数量限制，默认为3条。建议根据需要调整：简单查询用1-3条，复杂查询用5-10条',
                            'default' => 3,
                            'minimum' => 1,
                            'maximum' => 10
                        ],
                        'score_threshold' => [
                            'type' => 'number',
                            'description' => '相似度阈值，用于过滤搜索结果。默认0.8表示高相关性，0.6-0.7适合宽泛搜索，0.8-1.0适合精确搜索',
                            'default' => 0.8,
                            'minimum' => 0.0,
                            'maximum' => 1.0
                        ]
                    ],
                    'required' => ['query']
                ],
                'outputSchema' => [
                    'type' => 'object',
                    'properties' => [
                        'results' => [
                            'type' => 'array',
                            'description' => "从《{$this->book->name}》中搜索到的相关文档列表，按相似度排序",
                            'items' => [
                                'type' => 'object',
                                'properties' => [
                                    'title' => [
                                        'type' => 'string',
                                        'description' => '文档或章节的标题'
                                    ],
                                    'content' => [
                                        'type' => 'string',
                                        'description' => '与查询相关的文档内容片段，通常包含关键信息'
                                    ],
                                    'path' => [
                                        'type' => 'string',
                                        'description' => '文档在知识库中的路径，可用于定位具体位置'
                                    ],
                                    'score' => [
                                        'type' => 'number',
                                        'description' => '相似度分数(0.0-1.0)，分数越高表示与查询越相关'
                                    ]
                                ]
                            ]
                        ],
                        'total' => [
                            'type' => 'integer',
                            'description' => '本次搜索返回的结果总数'
                        ]
                    ]
                ]
            ]
        ];

        return [
            'tools' => $tools
        ];
    }

    /**
     * 调用工具
     */
    private function callTool($params)
    {
        if (!isset($params['name'])) {
            throw new \Exception('Missing required parameter: name');
        }

        $toolName = $params['name'];
        $arguments = $params['arguments'] ?? [];

        switch ($toolName) {
            case 'search_documents':
                return $this->handleSearchDocuments($arguments);
            default:
                throw new \Exception("Unknown tool: {$toolName}");
        }
    }

    /**
     * 处理文档搜索
     */
    private function handleSearchDocuments($arguments)
    {
        // 验证参数
        if (empty($arguments['query'])) {
            throw new \Exception('Missing required parameter: query');
        }

        $query = $arguments['query'];
        $limit = $arguments['limit'] ?? 3;
        $scoreThreshold = $arguments['score_threshold'] ?? 0.8;

        // 限制参数范围
        $limit = max(1, min(10, $limit));
        $scoreThreshold = max(0.0, min(1.0, $scoreThreshold));

        // 使用向量搜索
        $results = $this->book->getPages()->searchByVector($query, null, $limit, $scoreThreshold);

        $formattedResults = [];
        foreach ($results as $result) {
            $formattedResults[] = [
                'title' => $result['title'] ?? '未知标题',
                'content' => $result['content'] ?? '',
                'path' => $result['path'] ?? '',
                'score' => $result['score'] ?? 0.0
            ];
        }

        $responseData = [
            'results' => $formattedResults,
            'total' => count($formattedResults)
        ];

        return [
            'content' => [
                [
                    'type' => 'text',
                    'text' => json_encode($responseData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT)
                ]
            ],
            'structuredContent' => $responseData,
            'isError' => false
        ];
    }

    /**
     * JSON-RPC成功响应
     */
    private function jsonRpcSuccess($id, $result)
    {
        return json([
            'jsonrpc' => '2.0',
            'id' => $id,
            'result' => $result
        ]);
    }

    /**
     * JSON-RPC错误响应
     */
    private function jsonRpcError($id, $code, $message)
    {
        return json([
            'jsonrpc' => '2.0',
            'id' => $id,
            'error' => [
                'code' => $code,
                'message' => $message
            ]
        ]);
    }

    /**
     * 返回协议错误
     */
    private function errorResponse($code, $message)
    {
        return Response::create([
            'error' => [
                'code' => $code,
                'message' => $message
            ]
        ], 'json')->code(400);
    }
}
