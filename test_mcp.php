<?php
/**
 * MCP接口测试脚本
 */

// 配置
$baseUrl = 'http://localhost:8000'; // 根据实际情况修改
$bookHashId = 'your_book_hash_id'; // 替换为实际的book hash id
$token = 'your_access_token'; // 替换为实际的访问令牌

$mcpUrl = "{$baseUrl}/api/book/{$bookHashId}/mcp";

function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        $headers[] = 'Content-Type: application/json';
    }
    
    if ($headers) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'body' => $response
    ];
}

echo "=== MCP接口测试 ===\n\n";

// 测试1: 获取服务器信息
echo "1. 测试获取服务器信息 (GET)\n";
$response = makeRequest($mcpUrl, 'GET', null, [
    "Authorization: Bearer {$token}"
]);

echo "HTTP状态码: {$response['code']}\n";
echo "响应内容:\n";
echo json_encode(json_decode($response['body']), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// 测试2: 列出工具
echo "2. 测试列出工具 (tools/list)\n";
$toolsListRequest = [
    'jsonrpc' => '2.0',
    'id' => 1,
    'method' => 'tools/list',
    'params' => []
];

$response = makeRequest($mcpUrl, 'POST', $toolsListRequest, [
    "Authorization: Bearer {$token}"
]);

echo "HTTP状态码: {$response['code']}\n";
echo "响应内容:\n";
echo json_encode(json_decode($response['body']), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// 测试3: 调用搜索工具
echo "3. 测试调用搜索工具 (tools/call)\n";
$toolsCallRequest = [
    'jsonrpc' => '2.0',
    'id' => 2,
    'method' => 'tools/call',
    'params' => [
        'name' => 'search_documents',
        'arguments' => [
            'query' => '如何使用API',
            'limit' => 3,
            'score_threshold' => 0.7
        ]
    ]
];

$response = makeRequest($mcpUrl, 'POST', $toolsCallRequest, [
    "Authorization: Bearer {$token}"
]);

echo "HTTP状态码: {$response['code']}\n";
echo "响应内容:\n";
echo json_encode(json_decode($response['body']), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// 测试4: 测试错误处理 - 未知方法
echo "4. 测试错误处理 - 未知方法\n";
$errorRequest = [
    'jsonrpc' => '2.0',
    'id' => 3,
    'method' => 'unknown/method',
    'params' => []
];

$response = makeRequest($mcpUrl, 'POST', $errorRequest, [
    "Authorization: Bearer {$token}"
]);

echo "HTTP状态码: {$response['code']}\n";
echo "响应内容:\n";
echo json_encode(json_decode($response['body']), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

// 测试5: 测试错误处理 - 未知工具
echo "5. 测试错误处理 - 未知工具\n";
$unknownToolRequest = [
    'jsonrpc' => '2.0',
    'id' => 4,
    'method' => 'tools/call',
    'params' => [
        'name' => 'unknown_tool',
        'arguments' => []
    ]
];

$response = makeRequest($mcpUrl, 'POST', $unknownToolRequest, [
    "Authorization: Bearer {$token}"
]);

echo "HTTP状态码: {$response['code']}\n";
echo "响应内容:\n";
echo json_encode(json_decode($response['body']), JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n\n";

echo "=== 测试完成 ===\n";
