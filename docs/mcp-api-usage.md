# MCP API 使用说明

## 概述

本系统实现了 Model Context Protocol (MCP) 接口，提供文档检索工具，允许AI助手通过标准化的协议访问知识库内容。

## 接口地址

```
GET/POST /api/book/{book_hash_id}/mcp
```

## 认证

需要在请求头中包含空间访问令牌：

```
Authorization: Bearer {空间访问令牌}
```

## 支持的方法

### 1. 获取服务器信息 (GET)

**请求:**
```http
GET /api/book/{book_hash_id}/mcp
Authorization: Bearer {token}
```

**响应:**
```json
{
  "name": "TopThink Wiki Knowledge Base",
  "version": "1.0.0",
  "description": "知识库文档搜索服务",
  "capabilities": {
    "tools": {
      "listChanged": false
    }
  }
}
```

### 2. 列出可用工具 (POST)

**请求:**
```http
POST /api/book/{book_hash_id}/mcp
Content-Type: application/json
Authorization: Bearer {token}

{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/list",
  "params": {}
}
```

**响应:**
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "result": {
    "tools": [
      {
        "name": "search_documents",
        "title": "文档搜索 - {文档名称}",
        "description": "在《{文档名称}》知识库中搜索相关文档内容，支持关键词搜索和语义搜索。该知识库包含丰富的技术文档和资料，可以帮助您快速找到所需信息。",
        "inputSchema": {
          "type": "object",
          "properties": {
            "query": {
              "type": "string",
              "description": "搜索关键词或问题，支持自然语言查询"
            },
            "limit": {
              "type": "integer",
              "description": "返回结果数量限制，默认为3",
              "default": 3,
              "minimum": 1,
              "maximum": 10
            },
            "score_threshold": {
              "type": "number",
              "description": "相似度阈值，默认为0.8",
              "default": 0.8,
              "minimum": 0.0,
              "maximum": 1.0
            }
          },
          "required": ["query"]
        },
        "outputSchema": {
          "type": "object",
          "properties": {
            "results": {
              "type": "array",
              "description": "搜索结果列表"
            },
            "total": {
              "type": "integer",
              "description": "总结果数量"
            }
          }
        }
      }
    ]
  }
}
```

### 3. 调用工具 (POST)

**请求:**
```http
POST /api/book/{book_hash_id}/mcp
Content-Type: application/json
Authorization: Bearer {token}

{
  "jsonrpc": "2.0",
  "id": 2,
  "method": "tools/call",
  "params": {
    "name": "search_documents",
    "arguments": {
      "query": "如何使用API",
      "limit": 5,
      "score_threshold": 0.7
    }
  }
}
```

**响应:**
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "{\"results\":[{\"title\":\"API使用指南\",\"content\":\"本文档介绍了如何使用API...\",\"path\":\"api/guide.md\",\"score\":0.95}],\"total\":1}"
      }
    ],
    "structuredContent": {
      "results": [
        {
          "title": "API使用指南",
          "content": "本文档介绍了如何使用API...",
          "path": "api/guide.md",
          "score": 0.95
        }
      ],
      "total": 1
    },
    "isError": false
  }
}
```

## 错误处理

### 协议错误
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "error": {
    "code": -32601,
    "message": "Method not found"
  }
}
```

### 工具执行错误
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "搜索失败: 参数错误"
      }
    ],
    "isError": true
  }
}
```

## 在Claude Desktop中配置

在Claude Desktop的配置文件中添加：

```json
{
  "mcpServers": {
    "knowledge": {
      "url": "http://k.topthink.org/api/book/{book_hash_id}/mcp",
      "headers": {
        "Authorization": "Bearer {空间访问令牌}"
      }
    }
  }
}
```

## 功能特性

1. **语义搜索**: 使用向量搜索技术，支持语义相似度匹配
2. **权限控制**: 继承现有的文档访问权限系统
3. **参数验证**: 严格的输入参数验证和范围限制
4. **错误处理**: 完整的错误处理机制
5. **标准协议**: 完全符合MCP协议规范

## 注意事项

1. 需要确保文档已开启AI智能功能并完成向量化训练
2. 搜索结果的相似度分数范围为0.0-1.0，分数越高表示相关性越强
3. 建议根据实际需求调整相似度阈值和结果数量限制
